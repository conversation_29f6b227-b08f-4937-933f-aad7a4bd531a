from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import json
import os
import base64
from io import BytesIO
from PIL import Image
import numpy as np

# Initialize FastAPI app
app = FastAPI(
    title="AgroConnect Plant Disease Prediction API",
    description="API for predicting plant diseases and providing treatment recommendations",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class PredictionRequest(BaseModel):
    image_data: str  # Base64 encoded image
    crop_type: Optional[str] = None

class Treatment(BaseModel):
    type: str
    name: str
    activeIngredient: str
    dosage: str
    applicationMethod: str
    frequency: str
    safetyPeriod: str
    price: float
    productId: str

class Fertilizer(BaseModel):
    name: str
    composition: str
    dosage: str
    applicationMethod: str
    benefits: str
    price: float
    productId: str

class DiseaseInfo(BaseModel):
    id: str
    name: str
    crop: str
    symptoms: List[str]
    description: str
    image: str
    treatments: List[Treatment]
    fertilizers: List[Fertilizer]
    culturalPractices: List[str]
    prevention: List[str]

class PredictionResponse(BaseModel):
    disease_id: str
    disease_name: str
    crop: str
    confidence: float
    disease_info: DiseaseInfo

# Load disease data
def load_disease_data():
    """Load disease data from JSON file"""
    try:
        # Try to load from frontend data directory first
        frontend_path = os.path.join("..", "frontend", "src", "data", "diseases.json")
        if os.path.exists(frontend_path):
            with open(frontend_path, 'r') as f:
                return json.load(f)

        # Fallback to local copy
        local_path = "diseases.json"
        if os.path.exists(local_path):
            with open(local_path, 'r') as f:
                return json.load(f)

        # Return default data if no file found
        return {"diseases": []}
    except Exception as e:
        print(f"Error loading disease data: {e}")
        return {"diseases": []}

# Mock disease prediction function
def predict_disease(image_array: np.ndarray, crop_type: Optional[str] = None) -> tuple:
    """
    Mock function to predict plant disease from image
    In a real implementation, this would use a trained ML model
    """
    # For demonstration, we'll return a disease from our database
    # In a real implementation, image_array would be fed to an ML model
    disease_data = load_disease_data()
    diseases = disease_data.get("diseases", [])

    if not diseases:
        return None, 0.0

    # Simple mock logic - in reality, this would be ML model inference
    # Use image properties to simulate different predictions
    image_mean = np.mean(image_array) if image_array.size > 0 else 128

    if crop_type:
        # Filter diseases by crop type if provided
        filtered_diseases = [d for d in diseases if d["crop"].lower() == crop_type.lower()]
        if filtered_diseases:
            diseases = filtered_diseases

    # Mock selection based on image characteristics
    disease_index = int(image_mean) % len(diseases)
    selected_disease = diseases[disease_index]

    # Mock confidence based on image variance (higher variance = higher confidence)
    confidence = min(0.95, max(0.60, np.std(image_array) / 255.0 + 0.5))

    return selected_disease, confidence

def process_image(image_data: str) -> np.ndarray:
    """Process base64 image data and return numpy array"""
    try:
        # Remove data URL prefix if present
        if image_data.startswith('data:image'):
            image_data = image_data.split(',')[1]

        # Decode base64 image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(BytesIO(image_bytes))

        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Resize image (typical preprocessing for ML models)
        image = image.resize((224, 224))

        # Convert to numpy array
        image_array = np.array(image)

        return image_array
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing image: {str(e)}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AgroConnect Plant Disease Prediction API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "plant-disease-prediction"}

@app.get("/diseases")
async def get_diseases():
    """Get all available diseases in the database"""
    disease_data = load_disease_data()
    return disease_data

@app.get("/diseases/{disease_id}")
async def get_disease_by_id(disease_id: str):
    """Get specific disease information by ID"""
    disease_data = load_disease_data()
    diseases = disease_data.get("diseases", [])

    disease = next((d for d in diseases if d["id"] == disease_id), None)
    if not disease:
        raise HTTPException(status_code=404, detail="Disease not found")

    return disease

@app.post("/predict", response_model=PredictionResponse)
async def predict_plant_disease(request: PredictionRequest):
    """
    Predict plant disease from uploaded image
    """
    try:
        # Process the image
        image_array = process_image(request.image_data)

        # Predict disease
        disease_data, confidence = predict_disease(image_array, request.crop_type)

        if not disease_data:
            raise HTTPException(status_code=404, detail="No disease prediction available")

        # Convert disease data to response format
        treatments = [Treatment(**treatment) for treatment in disease_data.get("treatments", [])]
        fertilizers = [Fertilizer(**fertilizer) for fertilizer in disease_data.get("fertilizers", [])]

        disease_info = DiseaseInfo(
            id=disease_data["id"],
            name=disease_data["name"],
            crop=disease_data["crop"],
            symptoms=disease_data["symptoms"],
            description=disease_data["description"],
            image=disease_data.get("image", ""),
            treatments=treatments,
            fertilizers=fertilizers,
            culturalPractices=disease_data.get("culturalPractices", []),
            prevention=disease_data.get("prevention", [])
        )

        response = PredictionResponse(
            disease_id=disease_data["id"],
            disease_name=disease_data["name"],
            crop=disease_data["crop"],
            confidence=confidence,
            disease_info=disease_info
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/predict-file")
async def predict_from_file(file: UploadFile = File(...), crop_type: Optional[str] = None):
    """
    Predict plant disease from uploaded file
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Read and process image
        image_bytes = await file.read()
        image = Image.open(BytesIO(image_bytes))

        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Resize image
        image = image.resize((224, 224))
        image_array = np.array(image)

        # Predict disease
        disease_data, confidence = predict_disease(image_array, crop_type)

        if not disease_data:
            raise HTTPException(status_code=404, detail="No disease prediction available")

        # Convert disease data to response format
        treatments = [Treatment(**treatment) for treatment in disease_data.get("treatments", [])]
        fertilizers = [Fertilizer(**fertilizer) for fertilizer in disease_data.get("fertilizers", [])]

        disease_info = DiseaseInfo(
            id=disease_data["id"],
            name=disease_data["name"],
            crop=disease_data["crop"],
            symptoms=disease_data["symptoms"],
            description=disease_data["description"],
            image=disease_data.get("image", ""),
            treatments=treatments,
            fertilizers=fertilizers,
            culturalPractices=disease_data.get("culturalPractices", []),
            prevention=disease_data.get("prevention", [])
        )

        response = PredictionResponse(
            disease_id=disease_data["id"],
            disease_name=disease_data["name"],
            crop=disease_data["crop"],
            confidence=confidence,
            disease_info=disease_info
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/crops")
async def get_available_crops():
    """Get list of available crop types"""
    disease_data = load_disease_data()
    diseases = disease_data.get("diseases", [])

    # Extract unique crop types
    crops = list(set(disease["crop"] for disease in diseases))
    return {"crops": sorted(crops)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)